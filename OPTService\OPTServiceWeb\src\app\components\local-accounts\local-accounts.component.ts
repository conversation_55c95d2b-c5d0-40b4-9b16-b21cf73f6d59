import { Component, Ng<PERSON>one, OnInit } from '@angular/core';
import { FormArray, FormBuilder } from '@angular/forms';
import { NGXLogger } from 'ngx-logger';
import { Subscription } from 'rxjs';
import { LocalAccountCard } from 'src/app/core/models/LocalAccountCard.model';
import { LocalAccountCustomer } from 'src/app/core/models/localAccountCustomer.model';
import { LocalAccounts } from 'src/app/core/models/localAccounts.model';
import { ButtonHelper } from 'src/app/helpers/button.helper';
import { Constants } from 'src/app/helpers/constants';
import { LoadingService } from 'src/app/services/loading.service';
import { LocalAccountService } from 'src/app/services/local-account.service';
import { SignalRService } from 'src/app/services/signal-r.service';

class CustomerErrors {
  customer: boolean = false;
  name: boolean = false;
  transactionsAllowed: boolean = false;
  transactionLimit: boolean = false;
  pin: boolean  = false;
  printValue: boolean = false;
  allowLoyalty: boolean = false;
  fuelOnly: boolean = false;
  registrationEntry: boolean = false;
  creditStatus: boolean = false;
  balance: boolean = false;
  add: boolean = false;
}

class CollapseCard {
  collapse: boolean = true;
}

class CollapseCustomer {
  collapse: boolean = true;
  cards: boolean = true;
  cardList: Map<string, CollapseCard> = new Map<string, CollapseCard>();
}

@Component({
  selector: 'app-local-accounts',
  templateUrl: './local-accounts.component.html',
  styleUrls: ['./local-accounts.component.css']
})
export class LocalAccountsComponent implements OnInit {

  showContent: boolean = false;
  disableModification: boolean = true;
  localAccountsData: LocalAccounts;
  signalRData: Subscription | undefined;

  localAccountsForm = this.fb.group({
    newCustomer: this.fb.group(new LocalAccountCustomer()),
    localAccountCustomers: new FormArray([])
  });

  errors: Array<CustomerErrors> = [];
  collapseNewCustomer: boolean = true;
  collapseMap: Map<string, CollapseCustomer> = new Map<string, CollapseCustomer>();
  newCustomerError: boolean = false;

  constructor(
    private fb: FormBuilder,
    private signalRService: SignalRService,
    private zone: NgZone,
    private localAccountService: LocalAccountService,
    private logger: NGXLogger,
    private loadingService: LoadingService) {
    this.signalRData = this.signalRService.getLocalAccountsSignalRMessage().subscribe((data) => {
      this.zone.run(() => {
        const showLoading = data === Constants.SignalRRefreshItemId;
        this.refreshData(showLoading);
      });
    });
  }

  /**
   * Getter for localAccountCustomers form array
   */
  get localAccountCustomers(): FormArray {
    return this.localAccountsForm.get("localAccountCustomers") as FormArray;
  }

  /**
   * Adds the localAccountCustomer group form to the localAccountCustomers form array
   * @param customer customer to add to form array
   */
  patchLocalAccountCustomer(customer: LocalAccountCustomer) {
    let cardFormArray = new FormArray([]);
    customer.Cards.forEach(card => {
      cardFormArray.push(this.fb.group(card));
    });
    let customerGroup = {
      AllowLoyalty: customer.AllowLoyalty,
      Balance: customer.Balance,
      Cards: cardFormArray,
      CustomerReference: customer.CustomerReference,
      FuelOnly: customer.FuelOnly,
      LowCreditWarning: customer.LowCreditWarning,
      MaxCreditReached: customer.MaxCreditReached,
      MileageEntry: customer.MileageEntry,
      Name: customer.Name,
      Pin: customer.Pin,
      PrePayAccount: customer.PrePayAccount,
      PrintValue: customer.PrintValue,
      RegistrationEntry: customer.RegistrationEntry,
      TransactionLimit: customer.TransactionLimit,
      TransactionsAllowed: customer.TransactionsAllowed,
      CardPan: "",
      CardDescription: "",
      CardDiscount: 0
    };
    this.localAccountCustomers.push(this.fb.group(customerGroup));
  }

  /**
   * The refresh data method called when received a push from signalR
   */
  refreshData(showLoading: boolean = true): void {
    if (showLoading) {
      // Show loading screen
      this.showContent = false;
      this.loadingService.showLoadingScreen();
    }

    this.localAccountService.getLocalAccounts().subscribe(data => {
      this.logger.debug('Local account customers data ', data);
      this.localAccountsData = data;

      // Use setTimeout to defer form updates to avoid ExpressionChangedAfterItHasBeenCheckedError
      setTimeout(() => {
        this.errors = [];
        this.localAccountCustomers.clear();
        this.localAccountsData.LocalAccountCustomers.forEach(customer => {
          if (customer.Cards === null || customer.Cards === undefined) {
            customer.Cards = new Array<LocalAccountCard>();
          }
          this.patchLocalAccountCustomer(customer);
          this.addErrorCustomer(customer);
          this.addCollapseCustomer(customer);
        });

        this.logger.debug('collapseMap ', this.collapseMap);
        this.logger.debug('localAccountsForm ', this.localAccountsForm);
      }, 0);
    }, () => {
      this.logger.error('Problem getting local account customers details');
      this.loadingService.errorDuringLoading();
    }, () => {
      if (showLoading) {
        // Hide loading screen
        this.loadingService.hideLoadingScreen();
        this.showContent = true;
      }
    });
  }

  /**
   * Component initialization method
   */
  ngOnInit(): void {
    this.refreshData();
  }

  /**
   * Component destroy method
   */
  ngOnDestroy(): void {
    this.signalRData?.unsubscribe();
  }

  /**
   * Adds a new instance of errors for a given customer
   */
  addErrorCustomer(customer: LocalAccountCustomer) {
    this.errors.push(new CustomerErrors());
  }

  /**
   * Adds collapse control information object
   */
  addCollapseCustomer(customer: LocalAccountCustomer) {
    if (this.collapseMap.has(customer.CustomerReference) === false) {
      this.collapseMap.set(customer.CustomerReference, new CollapseCustomer());
      
      customer.Cards.forEach(card => {
        if (this.collapseMap.get(customer.CustomerReference).cardList.has(card.Pan) === false) {
          this.collapseMap.get(customer.CustomerReference).cardList.set(card.Pan, new CollapseCard());
        };
      });
    }
  }

  /**
   * Add a customer
   * @param index index of the form to use
   * @param error name of the error variable
   */
  addCustomer(index: number, error: string, event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.debug('Customer data ', this.localAccountsForm);

    this.logger.info('Updating customer ', index);
    this.localAccountService.addLocalAccountCustomer(this.localAccountsForm.value.localAccountCustomers[index]).subscribe(() => {
      this.logger.debug('Customer udpated ok');
      this.errors[index][error] = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating customer');
      this.errors[index][error] = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Creates a new customer
   */
  addNewCustomer(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.debug('Customer data ', this.localAccountsForm);

    this.logger.info('Creating new customer');
    this.localAccountService.addLocalAccountCustomer(this.localAccountsForm.value.newCustomer).subscribe(() => {
      this.logger.debug('Customer created ok');
      this.newCustomerError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem creating customer');
      this.newCustomerError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Removes a customer
   * @param index index of the form to remove
   * @param error name of the error variable
   */
  removeCustomer(index: number, error: string, event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.debug('Customer data ', this.localAccountsForm);

    this.logger.info('Removing customer ', index);
    this.localAccountService.removeLocalAccountCustomer(this.localAccountsForm.value.localAccountCustomers[index]).subscribe(() => {
      this.logger.debug('Customer removed ok');
      this.errors[index][error] = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem removing customer');
      this.errors[index][error] = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Sets customer balance
   * @param index index of the form to use
   * @param error name of the error variable
   */
  setCustomerBalance(index: number, error: string, event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.debug('Customer data ', this.localAccountsForm);

    this.logger.info('Updating balance for customer ', index);
    this.localAccountService.setLocalAccountCustomerBalance(this.localAccountsForm.value.localAccountCustomers[index]).subscribe(() => {
      this.logger.debug('Balance udpated ok');
      this.errors[index][error] = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating balance');
      this.errors[index][error] = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Adds a local account card
   * @param index index of the customer form to use
   * @param error name of the error variable
   */
  addNewCard(index: number, error: string, event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.debug('Customer data ', this.localAccountsForm);

    this.logger.info('Adding new card for customer ', index);
    let card = new LocalAccountCard();
    card.Pan = this.localAccountsForm.value.localAccountCustomers[index].CardPan
    card.CustomerReference = this.localAccountsForm.value.localAccountCustomers[index].CustomerReference;
    card.Description = this.localAccountsForm.value.localAccountCustomers[index].CardDescription;
    card.Discount = this.localAccountsForm.value.localAccountCustomers[index].CardDiscount;

    this.localAccountService.addLocalAccountCard(card).subscribe(() => {
      this.logger.debug('Card added ok');
      this.errors[index][error] = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem adding card');
      this.errors[index][error] = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Gets the credit status text based on customer flags
   * @param prePayAccount prePayAccount flag
   * @param lowCreditWarning lowCreditWarning flag
   * @param maxCreditReached maxCreditReached flag
   * @return credit status text
   */
  getCreditStatusText(prePayAccount: boolean, lowCreditWarning: boolean, maxCreditReached: boolean): string {
    if (prePayAccount) {
      return 'Pre pay account';      
    } else if (lowCreditWarning) {
      return 'Low credit warning';
    } else if (maxCreditReached) {
      return 'Max credit reached';
    }

    return 'None';
  }

  /**
   * Retrieves the credit status text
   * @param index index of the customer form to use
   * @param error name of the error variable
   */
  getCreditStatus(index: number): string {
    let customer = this.localAccountsForm.value.localAccountCustomers[index];
    return this.getCreditStatusText(customer.PrePayAccount, customer.LowCreditWarning, customer.MaxCreditReached);
  }

  /**
   * Sets the credit status  of a customer
   * @param i customer index
   * @param status status value
   */
  setCreditStatus(index: number, status: string): void {
    this.logger.debug('Customer data ', this.localAccountsForm);

    switch (status) {
      case 'PrePayAccount':
        this.localAccountsForm.value.localAccountCustomers[index].PrePayAccount = true;
        this.localAccountsForm.value.localAccountCustomers[index].LowCreditWarning = false;
        this.localAccountsForm.value.localAccountCustomers[index].MaxCreditReached = false;
        break;
      case 'LowCreditWarning':
        this.localAccountsForm.value.localAccountCustomers[index].PrePayAccount = false;
        this.localAccountsForm.value.localAccountCustomers[index].LowCreditWarning = true;
        this.localAccountsForm.value.localAccountCustomers[index].MaxCreditReached = false;
        break;
      case 'MaxCreditReached':
        this.localAccountsForm.value.localAccountCustomers[index].PrePayAccount = false;
        this.localAccountsForm.value.localAccountCustomers[index].LowCreditWarning = false;
        this.localAccountsForm.value.localAccountCustomers[index].MaxCreditReached = true;
        break;
      case 'None':
        this.localAccountsForm.value.localAccountCustomers[index].PrePayAccount = false;
        this.localAccountsForm.value.localAccountCustomers[index].LowCreditWarning = false;
        this.localAccountsForm.value.localAccountCustomers[index].MaxCreditReached = false;
        break;
      default:
        break;
    }
  }

  /**
   * Sets the credit status for a new customer
   * @param status credit status
   */
  setNewCustomerCreditStatus(status: string): void {
    this.logger.debug('Customer data ', this.localAccountsForm);

    switch (status) {
      case 'PrePayAccount':
        this.localAccountsForm.value.newCustomer.PrePayAccount = true;;
        this.localAccountsForm.value.newCustomer.LowCreditWarning = false;
        this.localAccountsForm.value.newCustomer.MaxCreditReached = false;
        break;
      case 'LowCreditWarning':
        this.localAccountsForm.value.newCustomer.PrePayAccount = false;;
        this.localAccountsForm.value.newCustomer.LowCreditWarning = true;
        this.localAccountsForm.value.newCustomer.MaxCreditReached = false;
        break;
      case 'MaxCreditReached':
        this.localAccountsForm.value.newCustomer.PrePayAccount = false;
        this.localAccountsForm.value.newCustomer.LowCreditWarning = false;
        this.localAccountsForm.value.newCustomer.MaxCreditReached = true;
        break;
      case 'None':
        this.localAccountsForm.value.newCustomer.PrePayAccount = false;
        this.localAccountsForm.value.newCustomer.LowCreditWarning = false;
        this.localAccountsForm.value.newCustomer.MaxCreditReached = false;
        break;
      default:
        break;
    }
  }

  /**
   * Gets the credit status text when creating a new customer
   */
  getNewCustomerCreditStatus(): string {
    let prePayAccount = this.localAccountsForm.value.newCustomer.PrePayAccount;
    let lowCreditWarning = this.localAccountsForm.value.newCustomer.LowCreditWarning;
    let maxCreditReached = this.localAccountsForm.value.newCustomer.MaxCreditReached;

    return this.getCreditStatusText(prePayAccount, lowCreditWarning, maxCreditReached);
  }  

  /**
   * Updated collapse flag for a given card
   * @param customerReference customer reference value
   * @param pan pan value
   */
  collapseChanged(customerReference: string, pan: string): void {
    this.logger.debug('CustomerReference ' + customerReference + " - PAN " + pan);
    this.collapseMap.get(customerReference).cardList.get(pan).collapse = !this.collapseMap.get(customerReference).cardList.get(pan).collapse;
  }
}
